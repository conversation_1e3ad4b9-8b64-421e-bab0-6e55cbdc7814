
  /* Conteneur du formulaire */
.form-container {
    width: 100%;
    max-width: 700px;
    margin: 50px auto;
    padding: 30px;
    background-color: #f9f9f9;
    border-radius: 8px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    font-family: Arial, sans-serif;
}

/* Titre du formulaire */
.form-container h2 {
    text-align: center;
    font-size: 2em;
    margin-bottom: 30px;
    color: #333;
}

/* Style des groupes de champs */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    font-size: 1.1em;
    font-weight: 500;
    color: #333;
    margin-bottom: 8px;
}

/* Champs de saisie */
.form-group input[type="text"],
.form-group input[type="number"],
.form-group input[type="file"],
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 12px;
    font-size: 1em;
    border: 1px solid #ccc;
    border-radius: 6px;
    box-sizing: border-box;
    margin-top: 5px;
}

/* Champs en focus */
.form-group input[type="text"]:focus,
.form-group input[type="number"]:focus,
.form-group input[type="file"]:focus,
.form-group select:focus,
.form-group textarea:focus {
    border-color: #007bff;
    outline: none;
    box-shadow: 0 0 5px rgba(0, 123, 255, 0.2);
}

/* Textarea pour la description */
.form-group textarea {
    resize: vertical;
    min-height: 150px;
}

/* Bouton de soumission */
.form-group .btn {
    width: 100%;
    padding: 12px;
    background-color: #007bff;
    color: white;
    font-size: 1.1em;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.form-group .btn:hover {
    background-color: #0056b3;
}

/* Message d'erreur ou de succès */
.form-message {
    text-align: center;
    font-size: 1.1em;
    margin-top: 15px;
    padding: 10px;
}

.form-message.success {
    color: #28a745; /* Vert */
}

.form-message.error {
    color: #dc3545; /* Rouge */
}

