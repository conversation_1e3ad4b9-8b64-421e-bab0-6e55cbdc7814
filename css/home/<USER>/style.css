/*
Error: Undefined variable: "$dip".
        on line 234 of scss/_home.scss
        from line 54 of scss/style.scss

Backtrace:
scss/_home.scss:234
scss/style.scss:54
C:/Devkit/Ruby23/lib/ruby/gems/2.3.0/gems/sass-3.6.0/lib/sass/script/tree/variable.rb:49:in `_perform'
C:/Devkit/Ruby23/lib/ruby/gems/2.3.0/gems/sass-3.6.0/lib/sass/script/tree/node.rb:50:in `perform'
C:/Devkit/Ruby23/lib/ruby/gems/2.3.0/gems/sass-3.6.0/lib/sass/tree/visitors/perform.rb:398:in `visit_prop'
C:/Devkit/Ruby23/lib/ruby/gems/2.3.0/gems/sass-3.6.0/lib/sass/tree/visitors/base.rb:36:in `visit'
C:/Devkit/Ruby23/lib/ruby/gems/2.3.0/gems/sass-3.6.0/lib/sass/tree/visitors/perform.rb:158:in `block in visit'
C:/Devkit/Ruby23/lib/ruby/gems/2.3.0/gems/sass-3.6.0/lib/sass/stack.rb:79:in `block in with_base'
C:/Devkit/Ruby23/lib/ruby/gems/2.3.0/gems/sass-3.6.0/lib/sass/stack.rb:135:in `with_frame'
C:/Devkit/Ruby23/lib/ruby/gems/2.3.0/gems/sass-3.6.0/lib/sass/stack.rb:79:in `with_base'
C:/Devkit/Ruby23/lib/ruby/gems/2.3.0/gems/sass-3.6.0/lib/sass/tree/visitors/perform.rb:158:in `visit'
C:/Devkit/Ruby23/lib/ruby/gems/2.3.0/gems/sass-3.6.0/lib/sass/tree/visitors/perform.rb:440:in `block (2 levels) in visit_rule'
C:/Devkit/Ruby23/lib/ruby/gems/2.3.0/gems/sass-3.6.0/lib/sass/tree/visitors/perform.rb:440:in `map'
C:/Devkit/Ruby23/lib/ruby/gems/2.3.0/gems/sass-3.6.0/lib/sass/tree/visitors/perform.rb:440:in `block in visit_rule'
C:/Devkit/Ruby23/lib/ruby/gems/2.3.0/gems/sass-3.6.0/lib/sass/tree/visitors/perform.rb:179:in `with_environment'
C:/Devkit/Ruby23/lib/ruby/gems/2.3.0/gems/sass-3.6.0/lib/sass/tree/visitors/perform.rb:438:in `visit_rule'
C:/Devkit/Ruby23/lib/ruby/gems/2.3.0/gems/sass-3.6.0/lib/sass/tree/visitors/base.rb:36:in `visit'
C:/Devkit/Ruby23/lib/ruby/gems/2.3.0/gems/sass-3.6.0/lib/sass/tree/visitors/perform.rb:158:in `block in visit'
C:/Devkit/Ruby23/lib/ruby/gems/2.3.0/gems/sass-3.6.0/lib/sass/stack.rb:79:in `block in with_base'
C:/Devkit/Ruby23/lib/ruby/gems/2.3.0/gems/sass-3.6.0/lib/sass/stack.rb:135:in `with_frame'
C:/Devkit/Ruby23/lib/ruby/gems/2.3.0/gems/sass-3.6.0/lib/sass/stack.rb:79:in `with_base'
C:/Devkit/Ruby23/lib/ruby/gems/2.3.0/gems/sass-3.6.0/lib/sass/tree/visitors/perform.rb:158:in `visit'
C:/Devkit/Ruby23/lib/ruby/gems/2.3.0/gems/sass-3.6.0/lib/sass/tree/visitors/perform.rb:440:in `block (2 levels) in visit_rule'
C:/Devkit/Ruby23/lib/ruby/gems/2.3.0/gems/sass-3.6.0/lib/sass/tree/visitors/perform.rb:440:in `map'
C:/Devkit/Ruby23/lib/ruby/gems/2.3.0/gems/sass-3.6.0/lib/sass/tree/visitors/perform.rb:440:in `block in visit_rule'
C:/Devkit/Ruby23/lib/ruby/gems/2.3.0/gems/sass-3.6.0/lib/sass/tree/visitors/perform.rb:179:in `with_environment'
C:/Devkit/Ruby23/lib/ruby/gems/2.3.0/gems/sass-3.6.0/lib/sass/tree/visitors/perform.rb:438:in `visit_rule'
C:/Devkit/Ruby23/lib/ruby/gems/2.3.0/gems/sass-3.6.0/lib/sass/tree/visitors/base.rb:36:in `visit'
C:/Devkit/Ruby23/lib/ruby/gems/2.3.0/gems/sass-3.6.0/lib/sass/tree/visitors/perform.rb:158:in `block in visit'
C:/Devkit/Ruby23/lib/ruby/gems/2.3.0/gems/sass-3.6.0/lib/sass/stack.rb:79:in `block in with_base'
C:/Devkit/Ruby23/lib/ruby/gems/2.3.0/gems/sass-3.6.0/lib/sass/stack.rb:135:in `with_frame'
C:/Devkit/Ruby23/lib/ruby/gems/2.3.0/gems/sass-3.6.0/lib/sass/stack.rb:79:in `with_base'
C:/Devkit/Ruby23/lib/ruby/gems/2.3.0/gems/sass-3.6.0/lib/sass/tree/visitors/perform.rb:158:in `visit'
C:/Devkit/Ruby23/lib/ruby/gems/2.3.0/gems/sass-3.6.0/lib/sass/tree/visitors/perform.rb:440:in `block (2 levels) in visit_rule'
C:/Devkit/Ruby23/lib/ruby/gems/2.3.0/gems/sass-3.6.0/lib/sass/tree/visitors/perform.rb:440:in `map'
C:/Devkit/Ruby23/lib/ruby/gems/2.3.0/gems/sass-3.6.0/lib/sass/tree/visitors/perform.rb:440:in `block in visit_rule'
C:/Devkit/Ruby23/lib/ruby/gems/2.3.0/gems/sass-3.6.0/lib/sass/tree/visitors/perform.rb:179:in `with_environment'
C:/Devkit/Ruby23/lib/ruby/gems/2.3.0/gems/sass-3.6.0/lib/sass/tree/visitors/perform.rb:438:in `visit_rule'
C:/Devkit/Ruby23/lib/ruby/gems/2.3.0/gems/sass-3.6.0/lib/sass/tree/visitors/base.rb:36:in `visit'
C:/Devkit/Ruby23/lib/ruby/gems/2.3.0/gems/sass-3.6.0/lib/sass/tree/visitors/perform.rb:158:in `block in visit'
C:/Devkit/Ruby23/lib/ruby/gems/2.3.0/gems/sass-3.6.0/lib/sass/stack.rb:79:in `block in with_base'
C:/Devkit/Ruby23/lib/ruby/gems/2.3.0/gems/sass-3.6.0/lib/sass/stack.rb:135:in `with_frame'
C:/Devkit/Ruby23/lib/ruby/gems/2.3.0/gems/sass-3.6.0/lib/sass/stack.rb:79:in `with_base'
C:/Devkit/Ruby23/lib/ruby/gems/2.3.0/gems/sass-3.6.0/lib/sass/tree/visitors/perform.rb:158:in `visit'
C:/Devkit/Ruby23/lib/ruby/gems/2.3.0/gems/sass-3.6.0/lib/sass/tree/visitors/perform.rb:325:in `block (2 levels) in visit_import'
C:/Devkit/Ruby23/lib/ruby/gems/2.3.0/gems/sass-3.6.0/lib/sass/tree/visitors/perform.rb:325:in `map'
C:/Devkit/Ruby23/lib/ruby/gems/2.3.0/gems/sass-3.6.0/lib/sass/tree/visitors/perform.rb:325:in `block in visit_import'
C:/Devkit/Ruby23/lib/ruby/gems/2.3.0/gems/sass-3.6.0/lib/sass/stack.rb:88:in `block in with_import'
C:/Devkit/Ruby23/lib/ruby/gems/2.3.0/gems/sass-3.6.0/lib/sass/stack.rb:135:in `with_frame'
C:/Devkit/Ruby23/lib/ruby/gems/2.3.0/gems/sass-3.6.0/lib/sass/stack.rb:88:in `with_import'
C:/Devkit/Ruby23/lib/ruby/gems/2.3.0/gems/sass-3.6.0/lib/sass/tree/visitors/perform.rb:322:in `visit_import'
C:/Devkit/Ruby23/lib/ruby/gems/2.3.0/gems/sass-3.6.0/lib/sass/tree/visitors/base.rb:36:in `visit'
C:/Devkit/Ruby23/lib/ruby/gems/2.3.0/gems/sass-3.6.0/lib/sass/tree/visitors/perform.rb:158:in `block in visit'
C:/Devkit/Ruby23/lib/ruby/gems/2.3.0/gems/sass-3.6.0/lib/sass/stack.rb:79:in `block in with_base'
C:/Devkit/Ruby23/lib/ruby/gems/2.3.0/gems/sass-3.6.0/lib/sass/stack.rb:135:in `with_frame'
C:/Devkit/Ruby23/lib/ruby/gems/2.3.0/gems/sass-3.6.0/lib/sass/stack.rb:79:in `with_base'
C:/Devkit/Ruby23/lib/ruby/gems/2.3.0/gems/sass-3.6.0/lib/sass/tree/visitors/perform.rb:158:in `visit'
C:/Devkit/Ruby23/lib/ruby/gems/2.3.0/gems/sass-3.6.0/lib/sass/tree/visitors/base.rb:52:in `block in visit_children'
C:/Devkit/Ruby23/lib/ruby/gems/2.3.0/gems/sass-3.6.0/lib/sass/tree/visitors/base.rb:52:in `map'
C:/Devkit/Ruby23/lib/ruby/gems/2.3.0/gems/sass-3.6.0/lib/sass/tree/visitors/base.rb:52:in `visit_children'
C:/Devkit/Ruby23/lib/ruby/gems/2.3.0/gems/sass-3.6.0/lib/sass/tree/visitors/perform.rb:167:in `block in visit_children'
C:/Devkit/Ruby23/lib/ruby/gems/2.3.0/gems/sass-3.6.0/lib/sass/tree/visitors/perform.rb:179:in `with_environment'
C:/Devkit/Ruby23/lib/ruby/gems/2.3.0/gems/sass-3.6.0/lib/sass/tree/visitors/perform.rb:166:in `visit_children'
C:/Devkit/Ruby23/lib/ruby/gems/2.3.0/gems/sass-3.6.0/lib/sass/tree/visitors/base.rb:36:in `block in visit'
C:/Devkit/Ruby23/lib/ruby/gems/2.3.0/gems/sass-3.6.0/lib/sass/tree/visitors/perform.rb:186:in `visit_root'
C:/Devkit/Ruby23/lib/ruby/gems/2.3.0/gems/sass-3.6.0/lib/sass/tree/visitors/base.rb:36:in `visit'
C:/Devkit/Ruby23/lib/ruby/gems/2.3.0/gems/sass-3.6.0/lib/sass/tree/visitors/perform.rb:157:in `visit'
C:/Devkit/Ruby23/lib/ruby/gems/2.3.0/gems/sass-3.6.0/lib/sass/tree/visitors/perform.rb:10:in `visit'
C:/Devkit/Ruby23/lib/ruby/gems/2.3.0/gems/sass-3.6.0/lib/sass/tree/root_node.rb:36:in `css_tree'
C:/Devkit/Ruby23/lib/ruby/gems/2.3.0/gems/sass-3.6.0/lib/sass/tree/root_node.rb:29:in `render_with_sourcemap'
C:/Devkit/Ruby23/lib/ruby/gems/2.3.0/gems/sass-3.6.0/lib/sass/engine.rb:389:in `_render_with_sourcemap'
C:/Devkit/Ruby23/lib/ruby/gems/2.3.0/gems/sass-3.6.0/lib/sass/engine.rb:307:in `render_with_sourcemap'
C:/Devkit/Ruby23/lib/ruby/gems/2.3.0/gems/sass-3.6.0/lib/sass/plugin/compiler.rb:462:in `update_stylesheet'
C:/Devkit/Ruby23/lib/ruby/gems/2.3.0/gems/sass-3.6.0/lib/sass/plugin/compiler.rb:215:in `block in update_stylesheets'
C:/Devkit/Ruby23/lib/ruby/gems/2.3.0/gems/sass-3.6.0/lib/sass/plugin/compiler.rb:209:in `each'
C:/Devkit/Ruby23/lib/ruby/gems/2.3.0/gems/sass-3.6.0/lib/sass/plugin/compiler.rb:209:in `update_stylesheets'
C:/Devkit/Ruby23/lib/ruby/gems/2.3.0/gems/sass-3.6.0/lib/sass/plugin/compiler.rb:443:in `on_file_changed'
C:/Devkit/Ruby23/lib/ruby/gems/2.3.0/gems/sass-3.6.0/lib/sass/plugin/compiler.rb:320:in `block in watch'
C:/Devkit/Ruby23/lib/ruby/gems/2.3.0/gems/sass-listen-4.0.0/lib/sass-listen/event/config.rb:23:in `call'
C:/Devkit/Ruby23/lib/ruby/gems/2.3.0/gems/sass-listen-4.0.0/lib/sass-listen/event/processor.rb:115:in `_process_changes'
C:/Devkit/Ruby23/lib/ruby/gems/2.3.0/gems/sass-listen-4.0.0/lib/sass-listen/event/processor.rb:19:in `block in loop_for'
C:/Devkit/Ruby23/lib/ruby/gems/2.3.0/gems/sass-listen-4.0.0/lib/sass-listen/event/processor.rb:15:in `loop'
C:/Devkit/Ruby23/lib/ruby/gems/2.3.0/gems/sass-listen-4.0.0/lib/sass-listen/event/processor.rb:15:in `loop_for'
C:/Devkit/Ruby23/lib/ruby/gems/2.3.0/gems/sass-listen-4.0.0/lib/sass-listen/event/loop.rb:84:in `_wait_for_changes'
C:/Devkit/Ruby23/lib/ruby/gems/2.3.0/gems/sass-listen-4.0.0/lib/sass-listen/event/loop.rb:42:in `block in setup'
C:/Devkit/Ruby23/lib/ruby/gems/2.3.0/gems/sass-listen-4.0.0/lib/sass-listen/internals/thread_pool.rb:6:in `block in add'
*/
body:before {
  white-space: pre;
  font-family: monospace;
  content: "Error: Undefined variable: \"$dip\".\A         on line 234 of scss/_home.scss\A         from line 54 of scss/style.scss"; }
