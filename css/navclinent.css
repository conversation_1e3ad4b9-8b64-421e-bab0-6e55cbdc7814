body {
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 0;
    background-color: #f4f4f4;
  }

  header {
    background-color: #333;
    color: #fff;
    text-align: center;
    padding: 10px;
  }

  main {
    max-width: 800px;
    margin: 20px auto;
  }

  .categories {
    list-style: none;
    padding: 0;
    margin: 0;
    display: flex;
    flex-wrap: wrap;
  }

  .category {
    background-color: #fff;
    border: 1px solid #ddd;
    border-radius: 5px;
    margin: 10px;
    padding: 20px;
    width: calc(33.33% - 20px);
    box-sizing: border-box;
    transition: transform 0.2s;
  }

  .category:hover {
    transform: scale(1.05);
  }

  .search-bar {
    display: flex;
    align-items: center;
    margin-left: auto;
  }

  .search-bar input {
    padding: 5px;
    border: none;
    border-radius: 3px;
    margin-right: 5px;
  }

  .search-bar button {
    padding: 5px 10px;
    border: none;
    background-color: #333;
    color: #fff;
    border-radius: 3px;
  }