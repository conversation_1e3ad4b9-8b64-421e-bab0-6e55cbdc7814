 a:focus, a:active { text-decoration: none; outline: none; @include transition(all 300ms ease 0s); }
input, select, textarea { outline: none; @include appearance( unset ); }
input::-webkit-outer-spin-button, input::-webkit-inner-spin-button {
    @include appearance( none );
    margin: 0;
}
input:focus, select:focus, textarea:focus { outline: none; @include box-shadow-input(none); }
input[type=checkbox] { @include appearance( checkbox ); }
input[type=radio] { @include appearance( radio ); }
img { max-width: 100%; height: auto; }
figure { margin: 0; }
p { margin-bottom: 0px; font-size: 15px; color: #777;}
h2 { 
    line-height: 1.66; 
    margin: 0; 
    padding: 0;
    font-weight: bold; 
    color: $black-color;
    font-family: Poppins;
    font-size: 36px;
}
.main { 
    background: #f8f8f8;
    padding: 150px 0;
}
.clear { clear: both; }
body {
	font-size: 13px;
    line-height: 1.8;
    color: $black-color;
    background: #f8f8f8;
    font-weight: 400;
    font-family: Poppins;
}
.container {
    width: 900px;
    background: #fff;
    margin: 0 auto;
    @include box-shadow(0px 15px 16.83px 0.17px rgba(0, 0, 0, 0.05));
    @include border-radius(20px);
}
  
.display-flex  {
    @extend display-flex;
    @include justify-content(space-between);
    @include align-items(center);
}

.display-flex-center  {
    @extend display-flex;
    @include justify-content(center);
    @include align-items(center);
}

.position-center {
    position: absolute;
    top: 50%;
    left: 50%;
    @include transform(translate(-50%,-50%));
}
